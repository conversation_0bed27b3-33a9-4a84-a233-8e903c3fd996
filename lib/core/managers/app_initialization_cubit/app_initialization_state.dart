part of 'app_initialization_cubit.dart';

abstract class AppInitializationState extends Equatable {
  const AppInitializationState();

  @override
  List<Object?> get props => [];
}

class AppInitializationInitial extends AppInitializationState {}

class AppInitializationLoading extends AppInitializationState {}

class AppInitializationShowOnboarding extends AppInitializationState {}

class AppInitializationNavigateToHome extends AppInitializationState {
  final UserEntity user;

  const AppInitializationNavigateToHome({required this.user});

  @override
  List<Object?> get props => [user];
}

class AppInitializationNavigateToLogin extends AppInitializationState {}

class AppInitializationError extends AppInitializationState {
  final String message;

  const AppInitializationError(this.message);

  @override
  List<Object?> get props => [message];
}
