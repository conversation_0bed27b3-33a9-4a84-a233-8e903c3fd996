import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/widgets/shimmer_components.dart';
import 'package:gather_point/core/utils/sound_manager.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/ServiceCategory.dart';
import 'package:gather_point/feature/home/<USER>/widgets/category_card_widget.dart';
import 'package:gather_point/feature/home/<USER>/views/explore_list_view.dart';
import 'package:hive/hive.dart';

class HomeCategoriesSection extends StatefulWidget {
  final List<ServiceCategory> categories;
  final bool isLoading;

  const HomeCategoriesSection({
    super.key,
    required this.categories,
    required this.isLoading,
  });

  @override
  State<HomeCategoriesSection> createState() => _HomeCategoriesSectionState();
}

class _HomeCategoriesSectionState extends State<HomeCategoriesSection> {
  final ScrollController _scrollController = ScrollController();
  bool _isScrolling = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (!_isScrolling && _scrollController.position.isScrollingNotifier.value) {
      _isScrolling = true;
      _playScrollSound();
    } else if (_isScrolling && !_scrollController.position.isScrollingNotifier.value) {
      _isScrolling = false;
    }
  }

  void _playScrollSound() async {
    try {
      await SoundManager.playScrollSound();
    } catch (e) {
      debugPrint('Scroll sound error: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return Column(
      children: [
        // Section Header
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Text(
                s.categories,
                style: AppTextStyles.font20Bold.copyWith(
                  color: context.primaryTextColor,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 5),

        // Categories List
        SizedBox(
          height: 95, // Original height for icon-based cards
          child: widget.isLoading
              ? ShimmerComponents.categoriesList(context)
              : widget.categories.isEmpty
                  ? _buildEmptyState(context)
                  : ListView.builder(
                      controller: _scrollController,
                      scrollDirection: Axis.horizontal,
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      itemCount: widget.categories.length,
                      itemBuilder: (context, index) {
                        final item = widget.categories[index];
                        return GestureDetector(
                          onTap: () async {
                            // Play click sound
                            try {
                              await SoundManager.playClickSound();
                            } catch (e) {
                              debugPrint('Click sound error: $e');
                            }

                            if (context.mounted) {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => ExploreListScreen(
                                    dioConsumer: DioConsumer(
                                      dio: getIt<Dio>(),
                                      profileBox: getIt<Box<UserEntity>>(),
                                    ),
                                    categoryId: item.id,
                                    categoryTitle: item.title,
                                  ),
                                ),
                              );
                            }
                          },
                          child: CategoryCardWidget(
                            title: item.title,
                            iconUrl: item.icon,
                          ),
                        );
                      },
                    ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.explore_outlined,
            size: 48,
            color: context.secondaryTextColor.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 12),
          Text(
            S.of(context).noResults,
            style: AppTextStyles.font14Regular.copyWith(
              color: context.secondaryTextColor,
            ),
          ),
        ],
      ),
    );
  }
}
