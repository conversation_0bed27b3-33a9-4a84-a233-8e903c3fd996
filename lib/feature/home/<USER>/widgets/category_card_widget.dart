import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';

class CategoryCardWidget extends StatefulWidget {
  final String title;
  final String? iconUrl;

  const CategoryCardWidget({
    super.key,
    required this.title,
    this.iconUrl,
  });

  @override
  State<CategoryCardWidget> createState() => _CategoryCardWidgetState();
}

class _CategoryCardWidgetState extends State<CategoryCardWidget>
    with TickerProviderStateMixin {
  late AnimationController _heartbeatController;
  late AnimationController _glowController;
  late Animation<double> _heartbeatAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();

    // Heartbeat animation for icon
    _heartbeatController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _heartbeatAnimation = Tween<double>(
      begin: 1.0,
      end: 1.15,
    ).animate(CurvedAnimation(
      parent: _heartbeatController,
      curve: Curves.easeInOut,
    ));

    // Glow animation for border
    _glowController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _glowController,
      curve: Curves.easeInOut,
    ));

    // Start animations with delay
    Future.delayed(Duration(milliseconds: 500 + (widget.title.hashCode % 1000)), () {
      if (mounted) {
        _startAnimations();
      }
    });
  }

  void _startAnimations() {
    _heartbeatController.repeat(reverse: true);
    _glowController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _heartbeatController.dispose();
    _glowController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_heartbeatAnimation, _glowAnimation]),
      builder: (context, child) {
        return Container(
          width: 90,
          margin: const EdgeInsets.only(right: 6, left: 6),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: context.accentColor.withValues(
                alpha: 0.8 + (0.4 * _glowAnimation.value),
              ),
              width: 1 + (0.5 * _glowAnimation.value),
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Transform.scale(
                scale: _heartbeatAnimation.value,
                child: Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: context.accentColor.withValues(
                      alpha: 0.1 + (0.1 * _glowAnimation.value),
                    ),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: context.accentColor.withValues(alpha: 0.2 * _glowAnimation.value),
                        blurRadius: 4 * _glowAnimation.value,
                        spreadRadius: 1 * _glowAnimation.value,
                      ),
                    ],
                  ),
                  child: Image.network(
                    widget.iconUrl ?? '',
                    width: 32,
                    height: 32,
                    errorBuilder: (context, error, stackTrace) => Icon(
                      Icons.category_outlined,
                      size: 32,
                      color: context.accentColor,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 4),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 2),
                child: Text(
                  widget.title,
                  style: AppTextStyles.font14Medium.copyWith(
                    color: context.primaryTextColor,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
