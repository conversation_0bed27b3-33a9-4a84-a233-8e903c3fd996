import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/feature/home/<USER>/models/place_detail_model.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/City.dart';

part 'explore_list_state.dart';

class ExploreListCubit extends Cubit<ExploreListState> {
  final DioConsumer dioConsumer;

  ExploreListCubit({required this.dioConsumer}) : super(const ExploreListInitial());

  int currentPage = 1;
  bool hasMore = true;
  List<PlaceDetailModel> allPlaces = [];

  /// Initialize explore list with category and optional city
  Future<void> initializeExploreList({
    required int categoryId,
    City? selectedCity,
  }) async {
    emit(const ExploreListLoading());
    
    currentPage = 1;
    hasMore = true;
    allPlaces.clear();

    await _fetchPlaces(categoryId: categoryId, selectedCity: selectedCity);
  }

  /// Fetch places with pagination
  Future<void> fetchPlaces({
    required int categoryId,
    City? selectedCity,
  }) async {
    if (state is ExploreListLoading && currentPage > 1) {
      // Show loading more state
      final currentState = state as ExploreListLoaded;
      emit(currentState.copyWith(isLoadingMore: true));
    }

    await _fetchPlaces(categoryId: categoryId, selectedCity: selectedCity);
  }

  /// Internal method to fetch places
  Future<void> _fetchPlaces({
    required int categoryId,
    City? selectedCity,
  }) async {
    try {
      String url = '/api/items/list?service_category_id=$categoryId&page=$currentPage';
      
      // Add city filter if selected
      if (selectedCity != null) {
        url += '&city_id=${selectedCity.id}';
      }

      final response = await dioConsumer.get(url);
      
      if (response['data'] != null) {
        final List data = response['data'];
        
        final fetchedPlaces = data.map<PlaceDetailModel>((item) {
          return PlaceDetailModel.fromJson(item);
        }).toList();

        if (currentPage == 1) {
          allPlaces = fetchedPlaces;
        } else {
          allPlaces.addAll(fetchedPlaces);
        }

        hasMore = fetchedPlaces.length >= 10;

        emit(ExploreListLoaded(
          places: List.from(allPlaces),
          filteredPlaces: List.from(allPlaces),
          selectedCity: selectedCity,
          hasMore: hasMore,
          currentPage: currentPage,
          isLoadingMore: false,
        ));
      }
    } catch (e) {
      emit(ExploreListError(e.toString()));
    }
  }

  /// Load more places (pagination)
  Future<void> loadMorePlaces({
    required int categoryId,
    City? selectedCity,
  }) async {
    if (!hasMore || state is! ExploreListLoaded) return;

    currentPage++;
    await fetchPlaces(categoryId: categoryId, selectedCity: selectedCity);
  }

  /// Apply filters to the places
  void applyFilters({
    String? searchQuery,
    RangeValues? priceRange,
    double? minRating,
    int? minGuests,
    String? sortBy,
  }) {
    if (state is! ExploreListLoaded) return;

    final currentState = state as ExploreListLoaded;
    List<PlaceDetailModel> filtered = List.from(allPlaces);

    // Apply search filter
    if (searchQuery != null && searchQuery.isNotEmpty) {
      final query = searchQuery.toLowerCase();
      filtered = filtered.where((place) {
        final title = place.title.toLowerCase();
        final description = place.content.toLowerCase();
        final city = place.city.toLowerCase();
        final country = place.country.toLowerCase();

        return title.contains(query) ||
               description.contains(query) ||
               city.contains(query) ||
               country.contains(query);
      }).toList();
    }

    // Apply price filter
    if (priceRange != null) {
      filtered = filtered.where((place) {
        return place.price >= priceRange.start && place.price <= priceRange.end;
      }).toList();
    }

    // Apply rating filter
    if (minRating != null && minRating > 0) {
      filtered = filtered.where((place) {
        final rating = place.rating ?? 0.0;
        return rating >= minRating;
      }).toList();
    }

    // Apply guests filter
    if (minGuests != null && minGuests > 1) {
      filtered = filtered.where((place) {
        final maxGuests = place.noGuests ?? 1;
        return maxGuests >= minGuests;
      }).toList();
    }

    // Apply sorting
    if (sortBy != null) {
      _applySorting(filtered, sortBy);
    }

    emit(currentState.copyWith(filteredPlaces: filtered));
  }

  /// Apply sorting to places
  void _applySorting(List<PlaceDetailModel> places, String sortBy) {
    switch (sortBy) {
      case 'price_low':
        places.sort((a, b) => a.price.compareTo(b.price));
        break;
      case 'price_high':
        places.sort((a, b) => b.price.compareTo(a.price));
        break;
      case 'rating_high':
        places.sort((a, b) {
          final ratingA = a.rating ?? 0.0;
          final ratingB = b.rating ?? 0.0;
          return ratingB.compareTo(ratingA);
        });
        break;
      case 'rating_low':
        places.sort((a, b) {
          final ratingA = a.rating ?? 0.0;
          final ratingB = b.rating ?? 0.0;
          return ratingA.compareTo(ratingB);
        });
        break;
      case 'newest':
        places.sort((a, b) {
          final dateA = DateTime.tryParse(a.createdAt) ?? DateTime.now();
          final dateB = DateTime.tryParse(b.createdAt) ?? DateTime.now();
          return dateB.compareTo(dateA);
        });
        break;
      case 'popular':
        places.sort((a, b) {
          final popularityA = (a.rating ?? 0.0) * 0.7 + (a.views * 0.3);
          final popularityB = (b.rating ?? 0.0) * 0.7 + (b.views * 0.3);
          return popularityB.compareTo(popularityA);
        });
        break;
    }
  }

  /// Toggle favorite status
  Future<void> toggleFavorite(PlaceDetailModel place) async {
    if (state is! ExploreListLoaded) return;

    final currentState = state as ExploreListLoaded;
    
    try {
      // TODO: Implement actual API call to toggle favorite
      // For now, simulate API call
      await Future.delayed(const Duration(milliseconds: 500));

      // Update local state
      final updatedPlaces = allPlaces.map((p) {
        if (p.id == place.id) {
          return p.copyWith(favorite: !p.favorite);
        }
        return p;
      }).toList();

      final updatedFilteredPlaces = currentState.filteredPlaces.map((p) {
        if (p.id == place.id) {
          return p.copyWith(favorite: !p.favorite);
        }
        return p;
      }).toList();

      allPlaces = updatedPlaces;

      emit(currentState.copyWith(
        places: updatedPlaces,
        filteredPlaces: updatedFilteredPlaces,
      ));
    } catch (e) {
      // Handle error silently or emit error state
    }
  }

  /// Refresh places
  Future<void> refreshPlaces({
    required int categoryId,
    City? selectedCity,
  }) async {
    currentPage = 1;
    hasMore = true;
    allPlaces.clear();
    
    await _fetchPlaces(categoryId: categoryId, selectedCity: selectedCity);
  }

  /// Update selected city
  void updateSelectedCity(City city, int categoryId) {
    if (state is ExploreListLoaded) {
      final currentState = state as ExploreListLoaded;
      emit(currentState.copyWith(selectedCity: city));

      // Refresh data with new city
      refreshPlaces(categoryId: categoryId, selectedCity: city);
    }
  }

  /// Search places with API call
  Future<void> searchPlaces({
    required int categoryId,
    City? selectedCity,
    required String searchQuery,
  }) async {
    emit(const ExploreListLoading());

    currentPage = 1;
    hasMore = true;
    allPlaces.clear();

    try {
      String url = '/api/items/search?service_category_id=$categoryId&keyword=${Uri.encodeComponent(searchQuery)}&page=$currentPage';

      // Add city filter if selected
      if (selectedCity != null) {
        url += '&city_id=${selectedCity.id}';
      }

      final response = await dioConsumer.get(url);

      if (response['data'] != null) {
        final List data = response['data'];

        final fetchedPlaces = data.map<PlaceDetailModel>((item) {
          return PlaceDetailModel.fromJson(item);
        }).toList();

        allPlaces = fetchedPlaces;
        hasMore = fetchedPlaces.length >= 10;

        emit(ExploreListLoaded(
          places: List.from(allPlaces),
          filteredPlaces: List.from(allPlaces),
          selectedCity: selectedCity,
          hasMore: hasMore,
          currentPage: currentPage,
          isLoadingMore: false,
        ));
      } else {
        // No results found
        emit(ExploreListLoaded(
          places: const [],
          filteredPlaces: const [],
          selectedCity: selectedCity,
          hasMore: false,
          currentPage: 1,
          isLoadingMore: false,
        ));
      }
    } catch (e) {
      emit(ExploreListError(e.toString()));
    }
  }
}
