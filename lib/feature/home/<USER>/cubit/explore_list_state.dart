part of 'explore_list_cubit.dart';

abstract class ExploreListState extends Equatable {
  const ExploreListState();

  @override
  List<Object?> get props => [];
}

class ExploreListInitial extends ExploreListState {
  const ExploreListInitial();
}

class ExploreListLoading extends ExploreListState {
  const ExploreListLoading();
}

class ExploreListLoaded extends ExploreListState {
  final List<PlaceDetailModel> places;
  final List<PlaceDetailModel> filteredPlaces;
  final City? selectedCity;
  final bool hasMore;
  final int currentPage;
  final bool isLoadingMore;

  const ExploreListLoaded({
    required this.places,
    required this.filteredPlaces,
    this.selectedCity,
    required this.hasMore,
    required this.currentPage,
    this.isLoadingMore = false,
  });

  ExploreListLoaded copyWith({
    List<PlaceDetailModel>? places,
    List<PlaceDetailModel>? filteredPlaces,
    City? selectedCity,
    bool? hasMore,
    int? currentPage,
    bool? isLoadingMore,
  }) {
    return ExploreListLoaded(
      places: places ?? this.places,
      filteredPlaces: filteredPlaces ?? this.filteredPlaces,
      selectedCity: selectedCity ?? this.selectedCity,
      hasMore: hasMore ?? this.hasMore,
      currentPage: currentPage ?? this.currentPage,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
    );
  }

  @override
  List<Object?> get props => [
        places,
        filteredPlaces,
        selectedCity,
        hasMore,
        currentPage,
        isLoadingMore,
      ];
}

class ExploreListError extends ExploreListState {
  final String message;

  const ExploreListError(this.message);

  @override
  List<Object> get props => [message];
}
