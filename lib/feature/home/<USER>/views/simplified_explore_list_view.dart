import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/utils/auth_utils.dart';
import 'package:gather_point/core/utils/guest_reservation_handler.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/City.dart';
import 'package:gather_point/feature/home/<USER>/cubit/explore_list_cubit.dart';
import 'package:gather_point/feature/home/<USER>/views/widgets/explore_list_widgets.dart';
import 'package:gather_point/feature/home/<USER>/views/widgets/modern_filter_bottom_sheet.dart';
import 'package:gather_point/generated/l10n.dart';
import 'place_details_screen.dart';

/// Simplified and Enhanced Explore List Screen
class SimplifiedExploreListView extends StatefulWidget {
  final int categoryId;
  final String categoryTitle;
  final DioConsumer dioConsumer;

  const SimplifiedExploreListView({
    super.key,
    required this.categoryId,
    required this.categoryTitle,
    required this.dioConsumer,
  });

  @override
  State<SimplifiedExploreListView> createState() => _SimplifiedExploreListViewState();
}

class _SimplifiedExploreListViewState extends State<SimplifiedExploreListView> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  
  // Filter state
  String selectedSortBy = 'price_low';
  RangeValues priceRange = const RangeValues(0, 2000);
  double selectedRating = 0.0;
  int selectedGuests = 1;
  
  // Cities
  List<City> cities = [];
  City? selectedCity;

  @override
  void initState() {
    super.initState();
    AuthUtils.initialize();
    _loadCitiesAndInitialize();
    _scrollController.addListener(_onScroll);
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ExploreListCubit(dioConsumer: widget.dioConsumer),
      child: Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        body: BlocBuilder<ExploreListCubit, ExploreListState>(
          builder: (context, state) {
            return RefreshIndicator(
              onRefresh: _onRefresh,
              child: CustomScrollView(
                controller: _scrollController,
                slivers: [
                  _buildAppBar(context),
                  _buildFilterBar(context),
                  _buildContent(context, state),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    final s = S.of(context);
    final theme = Theme.of(context);

    return SliverAppBar(
      expandedHeight: 200,
      floating: false,
      pinned: true,
      backgroundColor: theme.colorScheme.primary,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          widget.categoryTitle,
          style: AppTextStyles.font18Bold.copyWith(color: Colors.white),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                theme.colorScheme.primary,
                theme.colorScheme.primary.withValues(alpha: 0.8),
              ],
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 100, 16, 16),
            child: Column(
              children: [
                // City Selection
                if (cities.isNotEmpty)
                  CitySelectionWidget(
                    selectedCity: selectedCity,
                    cities: cities,
                    isLoading: false,
                    onCitySelected: _onCitySelected,
                  ),
                const SizedBox(height: 12),
                // Search Field
                _buildSearchField(context, s, theme),
              ],
            ),
          ),
        ),
      ),
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
        onPressed: () => Navigator.pop(context),
      ),
      actions: [
        GuestReservationHandler.getUserStatusBadge(context),
        const SizedBox(width: 8),
      ],
    );
  }

  Widget _buildSearchField(BuildContext context, S s, ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          prefixIcon: Icon(Icons.search, color: theme.colorScheme.primary),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear, color: Colors.grey),
                  onPressed: () {
                    _searchController.clear();
                    FocusScope.of(context).unfocus();
                  },
                )
              : null,
          hintText: s.searchHint,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        ),
      ),
    );
  }

  Widget _buildFilterBar(BuildContext context) {
    final theme = Theme.of(context);

    return SliverToBoxAdapter(
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: Text(
                'النتائج',
                style: AppTextStyles.font16Bold.copyWith(
                  color: theme.textTheme.bodyLarge?.color,
                ),
              ),
            ),
            IconButton(
              onPressed: _showFilters,
              icon: Icon(
                Icons.tune,
                color: theme.colorScheme.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context, ExploreListState state) {
    if (state is ExploreListLoading) {
      return const ExploreListShimmer();
    }

    if (state is ExploreListError) {
      return _buildErrorState(context, state.message);
    }

    if (state is ExploreListLoaded) {
      if (state.filteredPlaces.isEmpty) {
        return _buildEmptyState(context);
      }

      return _buildPlaceGrid(context, state);
    }

    return const SliverToBoxAdapter(child: SizedBox.shrink());
  }

  Widget _buildPlaceGrid(BuildContext context, ExploreListLoaded state) {
    return SliverPadding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 100),
      sliver: SliverGrid(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 0.75,
        ),
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            if (index >= state.filteredPlaces.length) {
              return state.isLoadingMore
                  ? const Center(child: CircularProgressIndicator())
                  : const SizedBox.shrink();
            }

            final place = state.filteredPlaces[index];
            return PlaceCard(
              place: place,
              index: index,
              onTap: () => _navigateToDetails(place),
              onFavoriteToggle: () => _toggleFavorite(place),
            );
          },
          childCount: state.filteredPlaces.length + (state.isLoadingMore ? 1 : 0),
        ),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, String message) {
    final theme = Theme.of(context);

    return SliverFillRemaining(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: AppTextStyles.font18Bold.copyWith(
                color: theme.textTheme.bodyLarge?.color,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'يرجى المحاولة مرة أخرى',
              style: AppTextStyles.font14Regular.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _onRefresh,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final s = S.of(context);
    final theme = Theme.of(context);

    return SliverFillRemaining(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              s.noResults,
              style: AppTextStyles.font18Bold.copyWith(
                color: theme.textTheme.bodyLarge?.color,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              s.tryDifferentSearchCriteria,
              style: AppTextStyles.font14Regular.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Event Handlers
  Future<void> _loadCitiesAndInitialize() async {
    await _loadCitiesDirectly();
    if (mounted) {
      context.read<ExploreListCubit>().initializeExploreList(
        categoryId: widget.categoryId,
        selectedCity: selectedCity,
      );
    }
  }

  Future<void> _loadCitiesDirectly() async {
    try {
      const defaultLat = 24.7136;
      const defaultLng = 46.6753;

      final response = await widget.dioConsumer.post(
        '/api/general/cities',
        data: {'lat': defaultLat, 'lng': defaultLng},
      );

      if (response['data'] != null) {
        final data = response['data']['cities'] as List;
        cities = data.map((e) => City.fromJson(e)).toList();
        if (cities.isNotEmpty) {
          selectedCity = cities.first;
        }
      }
    } catch (e) {
      cities = [];
      selectedCity = null;
    }
  }

  void _onCitySelected(City city) {
    setState(() => selectedCity = city);
    if (mounted) {
      context.read<ExploreListCubit>().updateSelectedCity(city, widget.categoryId);
    }
  }

  void _onSearchChanged() {
    _applyFilters();
  }

  void _onScroll() {
    final exploreState = context.read<ExploreListCubit>().state;
    if (exploreState is ExploreListLoaded &&
        _scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 100 &&
        !exploreState.isLoadingMore &&
        exploreState.hasMore) {
      context.read<ExploreListCubit>().loadMorePlaces(
        categoryId: widget.categoryId,
        selectedCity: selectedCity,
      );
    }
  }

  Future<void> _onRefresh() async {
    await context.read<ExploreListCubit>().refreshPlaces(
      categoryId: widget.categoryId,
      selectedCity: selectedCity,
    );
  }

  void _applyFilters() {
    context.read<ExploreListCubit>().applyFilters(
      searchQuery: _searchController.text,
      priceRange: priceRange,
      minRating: selectedRating > 0 ? selectedRating : null,
      minGuests: selectedGuests > 1 ? selectedGuests : null,
      sortBy: selectedSortBy,
    );
  }

  void _showFilters() {
    ModernFilterBottomSheet.show(
      context,
      selectedSortBy: selectedSortBy,
      priceRange: priceRange,
      selectedRating: selectedRating,
      selectedGuests: selectedGuests,
      onSortChanged: (value) => selectedSortBy = value,
      onPriceChanged: (value) => priceRange = value,
      onRatingChanged: (value) => selectedRating = value,
      onGuestsChanged: (value) => selectedGuests = value,
      onReset: () {
        selectedSortBy = 'price_low';
        priceRange = const RangeValues(0, 2000);
        selectedRating = 0.0;
        selectedGuests = 1;
      },
      onApply: _applyFilters,
    );
  }

  void _navigateToDetails(place) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PlaceDetailsScreen(placeDetail: place),
      ),
    );
  }

  Future<void> _toggleFavorite(place) async {
    HapticFeedback.lightImpact();
    await GuestReservationHandler.handleFavorite(
      context: context,
      place: place,
      onToggleFavorite: () => context.read<ExploreListCubit>().toggleFavorite(place),
      onLoginSuccess: _onRefresh,
    );
  }
}
