import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/utils/auth_utils.dart';
import 'package:gather_point/core/utils/guest_reservation_handler.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/City.dart';
import 'package:gather_point/feature/home/<USER>/cubit/explore_list_cubit.dart';
import 'package:gather_point/feature/home/<USER>/views/widgets/explore_app_bar.dart';
import 'package:gather_point/feature/home/<USER>/views/widgets/explore_filter_bar.dart';
import 'package:gather_point/feature/home/<USER>/views/widgets/explore_content_sections.dart';
import 'place_details_screen.dart';

/// Clean, simplified explore list screen - reduced from 900+ lines to ~200 lines
class SimplifiedExploreListView extends StatefulWidget {
  final int categoryId;
  final String categoryTitle;
  final DioConsumer dioConsumer;

  const SimplifiedExploreListView({
    super.key,
    required this.categoryId,
    required this.categoryTitle,
    required this.dioConsumer,
  });

  @override
  State<SimplifiedExploreListView> createState() => _SimplifiedExploreListViewState();
}

class _SimplifiedExploreListViewState extends State<SimplifiedExploreListView> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  late final ExploreListCubit _exploreListCubit;
  
  // Filter state
  String selectedSortBy = 'price_low';
  RangeValues priceRange = const RangeValues(0, 2000);
  double selectedRating = 0.0;
  int selectedGuests = 1;
  
  // Cities and location
  List<City> cities = [];
  City? selectedCity;
  double? currentLat;
  double? currentLng;
  bool isLoadingLocation = true;

  @override
  void initState() {
    super.initState();
    AuthUtils.initialize();
    _exploreListCubit = ExploreListCubit(dioConsumer: widget.dioConsumer);
    _scrollController.addListener(_onScroll);
    _searchController.addListener(_onSearchChanged);
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadLocationAndCities();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    _exploreListCubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _exploreListCubit,
      child: Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        body: BlocBuilder<ExploreListCubit, ExploreListState>(
          builder: (context, state) {
            return RefreshIndicator(
              onRefresh: _onRefresh,
              child: CustomScrollView(
                controller: _scrollController,
                slivers: [
                  ExploreAppBar(
                    categoryTitle: widget.categoryTitle,
                    cities: cities,
                    selectedCity: selectedCity,
                    isLoadingLocation: isLoadingLocation,
                    onCitySelected: _onCitySelected,
                    searchController: _searchController,
                  ),
                  ExploreFilterBar(
                    onFilterTap: _showFilters,
                  ),
                  ExploreContentSections(
                    state: state,
                    onRefresh: _onRefresh,
                    onPlaceTap: _navigateToDetails,
                    onFavoriteToggle: _toggleFavorite,
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  // Event Handlers - Simplified
  Future<void> _loadLocationAndCities() async {
    setState(() => isLoadingLocation = true);
    await _getCurrentLocation();
    await _loadCitiesWithLocation();
    
    if (mounted) {
      _exploreListCubit.initializeExploreList(
        categoryId: widget.categoryId,
        selectedCity: selectedCity,
      );
    }
    
    setState(() => isLoadingLocation = false);
  }

  Future<void> _getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        currentLat = 24.7136;
        currentLng = 46.6753;
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          currentLat = 24.7136;
          currentLng = 46.6753;
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        currentLat = 24.7136;
        currentLng = 46.6753;
        return;
      }

      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );
      
      currentLat = position.latitude;
      currentLng = position.longitude;
    } catch (e) {
      currentLat = 24.7136;
      currentLng = 46.6753;
    }
  }

  Future<void> _loadCitiesWithLocation() async {
    try {
      final response = await widget.dioConsumer.post(
        '/api/general/cities',
        data: {
          'lat': currentLat ?? 24.7136,
          'lng': currentLng ?? 46.6753,
        },
      );

      if (response['data'] != null) {
        final data = response['data']['cities'] as List;
        cities = data.map((e) => City.fromJson(e)).toList();
        
        if (cities.isNotEmpty) {
          selectedCity = cities.first;
        }
      }
    } catch (e) {
      cities = [];
      selectedCity = null;
    }
  }

  void _onCitySelected(City city) {
    setState(() => selectedCity = city);
    if (mounted) {
      _exploreListCubit.updateSelectedCity(city, widget.categoryId);
    }
  }

  void _onSearchChanged() {
    _performSearch();
  }

  Future<void> _performSearch() async {
    final query = _searchController.text.trim();
    
    if (query.isEmpty) {
      await _exploreListCubit.refreshPlaces(
        categoryId: widget.categoryId,
        selectedCity: selectedCity,
      );
      return;
    }

    await _exploreListCubit.searchPlaces(
      categoryId: widget.categoryId,
      selectedCity: selectedCity,
      searchQuery: query,
    );
  }

  void _onScroll() {
    final exploreState = _exploreListCubit.state;
    if (exploreState is ExploreListLoaded &&
        _scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 100 &&
        !exploreState.isLoadingMore &&
        exploreState.hasMore) {
      _exploreListCubit.loadMorePlaces(
        categoryId: widget.categoryId,
        selectedCity: selectedCity,
      );
    }
  }

  Future<void> _onRefresh() async {
    await _exploreListCubit.refreshPlaces(
      categoryId: widget.categoryId,
      selectedCity: selectedCity,
    );
  }

  void _showFilters() {
    // TODO: Implement filter bottom sheet
    HapticFeedback.lightImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Filter functionality coming soon')),
    );
  }

  void _navigateToDetails(place) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PlaceDetailsScreen(placeDetail: place),
      ),
    );
  }

  Future<void> _toggleFavorite(place) async {
    HapticFeedback.lightImpact();
    await GuestReservationHandler.handleFavorite(
      context: context,
      place: place,
      onToggleFavorite: () => _exploreListCubit.toggleFavorite(place),
      onLoginSuccess: _onRefresh,
    );
  }
}
