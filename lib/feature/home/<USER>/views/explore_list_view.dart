import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/utils/auth_utils.dart';
import 'package:gather_point/core/utils/guest_reservation_handler.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/City.dart';
import 'package:gather_point/feature/home/<USER>/cubit/explore_list_cubit.dart';
import 'package:gather_point/feature/home/<USER>/views/widgets/explore_list_widgets.dart';

import 'package:gather_point/generated/l10n.dart';
import 'place_details_screen.dart';

/// Enhanced Explore List Screen with modern UI and clean architecture
class ExploreListScreen extends StatefulWidget {
  final int categoryId;
  final String categoryTitle;
  final DioConsumer dioConsumer;

  const ExploreListScreen({
    super.key,
    required this.categoryId,
    required this.categoryTitle,
    required this.dioConsumer,
  });

  @override
  State<ExploreListScreen> createState() => _ExploreListScreenState();
}

class _ExploreListScreenState extends State<ExploreListScreen> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  
  // Cubit instance
  late final ExploreListCubit _exploreListCubit;
  
  // Filter state
  String selectedSortBy = 'price_low';
  RangeValues priceRange = const RangeValues(0, 2000);
  double selectedRating = 0.0;
  int selectedGuests = 1;
  
  // Cities and location
  List<City> cities = [];
  City? selectedCity;
  double? currentLat;
  double? currentLng;
  bool isLoadingLocation = true;

  @override
  void initState() {
    super.initState();
    AuthUtils.initialize();
    
    // Initialize cubit
    _exploreListCubit = ExploreListCubit(dioConsumer: widget.dioConsumer);
    
    _scrollController.addListener(_onScroll);
    _searchController.addListener(_onSearchChanged);
    
    // Initialize after the build is complete
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadLocationAndCities();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    _exploreListCubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _exploreListCubit,
      child: Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        body: BlocBuilder<ExploreListCubit, ExploreListState>(
          builder: (context, state) {
            return RefreshIndicator(
              onRefresh: _onRefresh,
              child: CustomScrollView(
                controller: _scrollController,
                slivers: [
                  _buildAppBar(context),
                  _buildFilterBar(context),
                  _buildContent(context, state),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    final s = S.of(context);
    final theme = Theme.of(context);

    return SliverAppBar(
      expandedHeight: 240, // Increased to accommodate city selection
      floating: false,
      pinned: true,
      backgroundColor: theme.colorScheme.primary,
      title: Text(
        widget.categoryTitle,
        style: AppTextStyles.font18Bold.copyWith(color: Colors.white),
      ),
      centerTitle: false, // Align title to the left (beside back button)
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                theme.colorScheme.primary,
                theme.colorScheme.primary.withValues(alpha: 0.8),
              ],
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 100, 16, 20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // City Selection
                CitySelectionWidget(
                  selectedCity: selectedCity,
                  cities: cities,
                  isLoading: isLoadingLocation,
                  onCitySelected: _onCitySelected,
                ),
                const SizedBox(height: 12),
                // Search Field
                Flexible(
                  child: _buildSearchField(context, s, theme),
                ),
              ],
            ),
          ),
        ),
      ),
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
        onPressed: () => Navigator.pop(context),
      ),
      actions: [
        GuestReservationHandler.getUserStatusBadge(context),
        const SizedBox(width: 8),
      ],
    );
  }

  Widget _buildSearchField(BuildContext context, S s, ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          prefixIcon: Icon(Icons.search, color: theme.colorScheme.primary),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear, color: Colors.grey),
                  onPressed: () {
                    _searchController.clear();
                    FocusScope.of(context).unfocus();
                  },
                )
              : null,
          hintText: s.searchHint,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        ),
      ),
    );
  }

  Widget _buildFilterBar(BuildContext context) {
    final s = S.of(context);
    final theme = Theme.of(context);

    return SliverToBoxAdapter(
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: Text(
                s.searchResults,
                style: AppTextStyles.font16Bold.copyWith(
                  color: theme.textTheme.bodyLarge?.color,
                ),
              ),
            ),
            IconButton(
              onPressed: _showFilters,
              icon: Icon(
                Icons.tune,
                color: theme.colorScheme.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context, ExploreListState state) {
    if (state is ExploreListLoading) {
      return const ExploreListShimmer();
    }

    if (state is ExploreListError) {
      return _buildErrorState(context, state.message);
    }

    if (state is ExploreListLoaded) {
      if (state.filteredPlaces.isEmpty) {
        return _buildEmptyState(context);
      }

      return _buildPlaceGrid(context, state);
    }

    return const SliverToBoxAdapter(child: SizedBox.shrink());
  }

  Widget _buildPlaceGrid(BuildContext context, ExploreListLoaded state) {
    return SliverPadding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 100),
      sliver: SliverGrid(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 0.8, // Increased to give more height for content
        ),
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            if (index >= state.filteredPlaces.length) {
              return state.isLoadingMore
                  ? const Center(child: CircularProgressIndicator())
                  : const SizedBox.shrink();
            }

            final place = state.filteredPlaces[index];
            return PlaceCard(
              place: place,
              index: index,
              onTap: () => _navigateToDetails(place),
              onFavoriteToggle: () => _toggleFavorite(place),
            );
          },
          childCount: state.filteredPlaces.length + (state.isLoadingMore ? 1 : 0),
        ),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, String message) {
    final s = S.of(context);
    final theme = Theme.of(context);

    return SliverFillRemaining(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text(
              s.error,
              style: AppTextStyles.font18Bold.copyWith(
                color: theme.textTheme.bodyLarge?.color,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              s.tryDifferentSearchCriteria,
              style: AppTextStyles.font14Regular.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _onRefresh,
              child: Text(s.cancel), // Using cancel as retry for now
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final s = S.of(context);
    final theme = Theme.of(context);

    return SliverFillRemaining(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              s.noResults,
              style: AppTextStyles.font18Bold.copyWith(
                color: theme.textTheme.bodyLarge?.color,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              s.tryDifferentSearchCriteria,
              style: AppTextStyles.font14Regular.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Event Handlers
  Future<void> _loadLocationAndCities() async {
    setState(() => isLoadingLocation = true);

    // Try to get current location
    await _getCurrentLocation();

    // Load cities with current location or default
    await _loadCitiesWithLocation();

    // Initialize explore list
    if (mounted) {
      _exploreListCubit.initializeExploreList(
        categoryId: widget.categoryId,
        selectedCity: selectedCity,
      );
    }

    setState(() => isLoadingLocation = false);
  }

  Future<void> _getCurrentLocation() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        // Use default Riyadh location
        currentLat = 24.7136;
        currentLng = 46.6753;
        return;
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          // Use default location
          currentLat = 24.7136;
          currentLng = 46.6753;
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        // Use default location
        currentLat = 24.7136;
        currentLng = 46.6753;
        return;
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      currentLat = position.latitude;
      currentLng = position.longitude;
    } catch (e) {
      // Use default Riyadh location if any error occurs
      currentLat = 24.7136;
      currentLng = 46.6753;
    }
  }

  Future<void> _loadCitiesWithLocation() async {
    try {
      final response = await widget.dioConsumer.post(
        '/api/general/cities',
        data: {
          'lat': currentLat ?? 24.7136,
          'lng': currentLng ?? 46.6753,
        },
      );

      if (response['data'] != null) {
        final data = response['data']['cities'] as List;
        cities = data.map((e) => City.fromJson(e)).toList();

        // Select the closest city (first in the list since API returns sorted by distance)
        if (cities.isNotEmpty) {
          selectedCity = cities.first;
        }
      }
    } catch (e) {
      cities = [];
      selectedCity = null;
    }
  }

  void _onCitySelected(City city) {
    setState(() => selectedCity = city);
    if (mounted) {
      _exploreListCubit.updateSelectedCity(city, widget.categoryId);
    }
  }

  void _onSearchChanged() {
    // Implement search with API call instead of just filtering
    _performSearch();
  }

  Future<void> _performSearch() async {
    final query = _searchController.text.trim();

    if (query.isEmpty) {
      // If search is empty, reload original data
      await _exploreListCubit.refreshPlaces(
        categoryId: widget.categoryId,
        selectedCity: selectedCity,
      );
      return;
    }

    // Perform search with API call
    await _exploreListCubit.searchPlaces(
      categoryId: widget.categoryId,
      selectedCity: selectedCity,
      searchQuery: query,
    );
  }

  void _onScroll() {
    final exploreState = _exploreListCubit.state;
    if (exploreState is ExploreListLoaded &&
        _scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 100 &&
        !exploreState.isLoadingMore &&
        exploreState.hasMore) {
      _exploreListCubit.loadMorePlaces(
        categoryId: widget.categoryId,
        selectedCity: selectedCity,
      );
    }
  }

  Future<void> _onRefresh() async {
    await _exploreListCubit.refreshPlaces(
      categoryId: widget.categoryId,
      selectedCity: selectedCity,
    );
  }

  void _applyFilters() {
    _exploreListCubit.applyFilters(
      searchQuery: _searchController.text,
      priceRange: priceRange,
      minRating: selectedRating > 0 ? selectedRating : null,
      minGuests: selectedGuests > 1 ? selectedGuests : null,
      sortBy: selectedSortBy,
    );
  }

  void _showFilters() {
    HapticFeedback.lightImpact();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: true,
      isDismissible: true,
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) => _buildSimpleFilterSheet(context, setModalState),
      ),
    );
  }

  Widget _buildSimpleFilterSheet(BuildContext context, StateSetter setModalState) {
    final s = S.of(context);
    final theme = Theme.of(context);
    final mediaQuery = MediaQuery.of(context);

    return SafeArea(
      child: Container(
        height: mediaQuery.size.height * 0.8,
        constraints: BoxConstraints(
          maxHeight: mediaQuery.size.height - mediaQuery.padding.top - 50,
        ),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 20,
            offset: const Offset(0, -4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(24),
            child: Row(
              children: [
                Text(
                  s.filterResults,
                  style: AppTextStyles.font20Bold.copyWith(
                    color: theme.textTheme.bodyLarge?.color,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    setModalState(() {
                      selectedSortBy = 'price_low';
                      priceRange = const RangeValues(0, 2000);
                      selectedRating = 0.0;
                      selectedGuests = 1;
                    });
                  },
                  child: Text(
                    s.resetFilters,
                    style: AppTextStyles.font14SemiBold.copyWith(
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSortSection(theme, setModalState),
                  const SizedBox(height: 32),
                  _buildPriceSection(theme, setModalState),
                  const SizedBox(height: 32),
                  _buildRatingSection(theme, setModalState),
                  const SizedBox(height: 32),
                  _buildGuestsSection(theme, setModalState),
                ],
              ),
            ),
          ),

          // Footer
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(
                  color: Colors.grey.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      s.cancel,
                      style: AppTextStyles.font16SemiBold,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  flex: 2,
                  child: ElevatedButton(
                    onPressed: () {
                      _applyFilters();
                      Navigator.pop(context);
                    },
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      s.applyFilters,
                      style: AppTextStyles.font16SemiBold.copyWith(
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ),
    );
  }

  Widget _buildSortSection(ThemeData theme, StateSetter setModalState) {
    final s = S.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          s.sortBy,
          style: AppTextStyles.font16Bold.copyWith(
            color: theme.textTheme.bodyLarge?.color,
          ),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: [
            _buildSortChip('price_low', s.priceLowToHigh, theme, setModalState),
            _buildSortChip('price_high', s.priceHighToLow, theme, setModalState),
            _buildSortChip('rating_high', s.ratingHighToLow, theme, setModalState),
            _buildSortChip('popular', s.popular, theme, setModalState),
            _buildSortChip('newest', s.newest, theme, setModalState),
          ],
        ),
      ],
    );
  }

  Widget _buildSortChip(String value, String label, ThemeData theme, StateSetter setModalState) {
    final isSelected = selectedSortBy == value;

    return FilterChip(
      selected: isSelected,
      label: Text(
        label,
        style: AppTextStyles.font14Regular.copyWith(
          color: isSelected ? Colors.white : theme.textTheme.bodyLarge?.color,
        ),
      ),
      onSelected: (selected) {
        if (selected) {
          setModalState(() {
            selectedSortBy = value;
          });
          HapticFeedback.lightImpact();
        }
      },
      backgroundColor: theme.cardColor,
      selectedColor: theme.colorScheme.primary,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
        side: BorderSide(
          color: isSelected ? theme.colorScheme.primary : Colors.grey[300]!,
        ),
      ),
    );
  }

  Widget _buildPriceSection(ThemeData theme, StateSetter setModalState) {
    final s = S.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          s.priceRange,
          style: AppTextStyles.font16Bold.copyWith(
            color: theme.textTheme.bodyLarge?.color,
          ),
        ),
        const SizedBox(height: 16),
        RangeSlider(
          values: priceRange,
          min: 0,
          max: 2000,
          divisions: 20,
          labels: RangeLabels(
            '${priceRange.start.round()} ريال',
            '${priceRange.end.round()} ريال',
          ),
          onChanged: (values) {
            setModalState(() {
              priceRange = values;
            });
            HapticFeedback.selectionClick();
          },
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '${priceRange.start.round()} ريال',
              style: AppTextStyles.font14Regular.copyWith(
                color: Colors.grey[600],
              ),
            ),
            Text(
              '${priceRange.end.round()} ريال',
              style: AppTextStyles.font14Regular.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRatingSection(ThemeData theme, StateSetter setModalState) {
    final s = S.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          s.minimumRating,
          style: AppTextStyles.font16Bold.copyWith(
            color: theme.textTheme.bodyLarge?.color,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: List.generate(5, (index) {
            final rating = index + 1.0;
            final isSelected = selectedRating >= rating;

            return GestureDetector(
              onTap: () {
                setModalState(() {
                  selectedRating = selectedRating == rating ? 0.0 : rating;
                });
                HapticFeedback.lightImpact();
              },
              child: Container(
                margin: const EdgeInsets.only(right: 8),
                child: Icon(
                  isSelected ? Icons.star : Icons.star_border,
                  color: Colors.amber,
                  size: 32,
                ),
              ),
            );
          }),
        ),
        const SizedBox(height: 8),
        Text(
          selectedRating > 0 ? '${selectedRating.toInt()} نجوم وأكثر' : 'جميع التقييمات',
          style: AppTextStyles.font14Regular.copyWith(
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildGuestsSection(ThemeData theme, StateSetter setModalState) {
    final s = S.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          s.guests,
          style: AppTextStyles.font16Bold.copyWith(
            color: theme.textTheme.bodyLarge?.color,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            IconButton(
              onPressed: selectedGuests > 1
                  ? () {
                      setModalState(() {
                        selectedGuests--;
                      });
                      HapticFeedback.lightImpact();
                    }
                  : null,
              icon: const Icon(Icons.remove_circle_outline),
              color: theme.colorScheme.primary,
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '$selectedGuests',
                style: AppTextStyles.font16Bold.copyWith(
                  color: theme.textTheme.bodyLarge?.color,
                ),
              ),
            ),
            IconButton(
              onPressed: selectedGuests < 20
                  ? () {
                      setModalState(() {
                        selectedGuests++;
                      });
                      HapticFeedback.lightImpact();
                    }
                  : null,
              icon: const Icon(Icons.add_circle_outline),
              color: theme.colorScheme.primary,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                selectedGuests == 1 ? 'ضيف واحد' : '$selectedGuests ضيوف',
                style: AppTextStyles.font14Regular.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _navigateToDetails(place) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PlaceDetailsScreen(placeDetail: place),
      ),
    );
  }

  Future<void> _toggleFavorite(place) async {
    HapticFeedback.lightImpact();
    await GuestReservationHandler.handleFavorite(
      context: context,
      place: place,
      onToggleFavorite: () => _exploreListCubit.toggleFavorite(place),
      onLoginSuccess: _onRefresh,
    );
  }
}
