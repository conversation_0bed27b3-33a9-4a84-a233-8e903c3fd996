import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/utils/auth_utils.dart';
import 'package:gather_point/core/utils/guest_reservation_handler.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/City.dart';
import 'package:gather_point/feature/home/<USER>/cubit/explore_list_cubit.dart';
import 'package:gather_point/feature/home/<USER>/views/widgets/explore_list_widgets.dart';
import 'package:gather_point/feature/home/<USER>/views/widgets/modern_filter_bottom_sheet.dart';
import 'package:gather_point/generated/l10n.dart';
import 'place_details_screen.dart';

/// Enhanced Explore List Screen with modern UI and clean architecture
class ExploreListScreen extends StatefulWidget {
  final int categoryId;
  final String categoryTitle;
  final DioConsumer dioConsumer;

  const ExploreListScreen({
    super.key,
    required this.categoryId,
    required this.categoryTitle,
    required this.dioConsumer,
  });

  @override
  State<ExploreListScreen> createState() => _ExploreListScreenState();
}

class _ExploreListScreenState extends State<ExploreListScreen> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  
  // Cubit instance
  late final ExploreListCubit _exploreListCubit;
  
  // Filter state
  String selectedSortBy = 'price_low';
  RangeValues priceRange = const RangeValues(0, 2000);
  double selectedRating = 0.0;
  int selectedGuests = 1;
  
  // Cities and location
  List<City> cities = [];
  City? selectedCity;
  double? currentLat;
  double? currentLng;
  bool isLoadingLocation = true;

  @override
  void initState() {
    super.initState();
    AuthUtils.initialize();
    
    // Initialize cubit
    _exploreListCubit = ExploreListCubit(dioConsumer: widget.dioConsumer);
    
    _scrollController.addListener(_onScroll);
    _searchController.addListener(_onSearchChanged);
    
    // Initialize after the build is complete
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadLocationAndCities();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    _exploreListCubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _exploreListCubit,
      child: Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        body: BlocBuilder<ExploreListCubit, ExploreListState>(
          builder: (context, state) {
            return RefreshIndicator(
              onRefresh: _onRefresh,
              child: CustomScrollView(
                controller: _scrollController,
                slivers: [
                  _buildAppBar(context),
                  _buildFilterBar(context),
                  _buildContent(context, state),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    final s = S.of(context);
    final theme = Theme.of(context);

    return SliverAppBar(
      expandedHeight: 240, // Increased to accommodate city selection
      floating: false,
      pinned: true,
      backgroundColor: theme.colorScheme.primary,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          widget.categoryTitle,
          style: AppTextStyles.font18Bold.copyWith(color: Colors.white),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                theme.colorScheme.primary,
                theme.colorScheme.primary.withValues(alpha: 0.8),
              ],
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 100, 16, 20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // City Selection
                CitySelectionWidget(
                  selectedCity: selectedCity,
                  cities: cities,
                  isLoading: isLoadingLocation,
                  onCitySelected: _onCitySelected,
                ),
                const SizedBox(height: 12),
                // Search Field
                Flexible(
                  child: _buildSearchField(context, s, theme),
                ),
              ],
            ),
          ),
        ),
      ),
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
        onPressed: () => Navigator.pop(context),
      ),
      actions: [
        GuestReservationHandler.getUserStatusBadge(context),
        const SizedBox(width: 8),
      ],
    );
  }

  Widget _buildSearchField(BuildContext context, S s, ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          prefixIcon: Icon(Icons.search, color: theme.colorScheme.primary),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear, color: Colors.grey),
                  onPressed: () {
                    _searchController.clear();
                    FocusScope.of(context).unfocus();
                  },
                )
              : null,
          hintText: s.searchHint,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        ),
      ),
    );
  }

  Widget _buildFilterBar(BuildContext context) {
    final theme = Theme.of(context);

    return SliverToBoxAdapter(
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: Text(
                'النتائج',
                style: AppTextStyles.font16Bold.copyWith(
                  color: theme.textTheme.bodyLarge?.color,
                ),
              ),
            ),
            IconButton(
              onPressed: _showFilters,
              icon: Icon(
                Icons.tune,
                color: theme.colorScheme.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context, ExploreListState state) {
    if (state is ExploreListLoading) {
      return const ExploreListShimmer();
    }

    if (state is ExploreListError) {
      return _buildErrorState(context, state.message);
    }

    if (state is ExploreListLoaded) {
      if (state.filteredPlaces.isEmpty) {
        return _buildEmptyState(context);
      }

      return _buildPlaceGrid(context, state);
    }

    return const SliverToBoxAdapter(child: SizedBox.shrink());
  }

  Widget _buildPlaceGrid(BuildContext context, ExploreListLoaded state) {
    return SliverPadding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 100),
      sliver: SliverGrid(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 0.75,
        ),
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            if (index >= state.filteredPlaces.length) {
              return state.isLoadingMore
                  ? const Center(child: CircularProgressIndicator())
                  : const SizedBox.shrink();
            }

            final place = state.filteredPlaces[index];
            return PlaceCard(
              place: place,
              index: index,
              onTap: () => _navigateToDetails(place),
              onFavoriteToggle: () => _toggleFavorite(place),
            );
          },
          childCount: state.filteredPlaces.length + (state.isLoadingMore ? 1 : 0),
        ),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, String message) {
    final theme = Theme.of(context);

    return SliverFillRemaining(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: AppTextStyles.font18Bold.copyWith(
                color: theme.textTheme.bodyLarge?.color,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'يرجى المحاولة مرة أخرى',
              style: AppTextStyles.font14Regular.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _onRefresh,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final s = S.of(context);
    final theme = Theme.of(context);

    return SliverFillRemaining(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              s.noResults,
              style: AppTextStyles.font18Bold.copyWith(
                color: theme.textTheme.bodyLarge?.color,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              s.tryDifferentSearchCriteria,
              style: AppTextStyles.font14Regular.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Event Handlers
  Future<void> _loadLocationAndCities() async {
    setState(() => isLoadingLocation = true);

    // Try to get current location
    await _getCurrentLocation();

    // Load cities with current location or default
    await _loadCitiesWithLocation();

    // Initialize explore list
    if (mounted) {
      _exploreListCubit.initializeExploreList(
        categoryId: widget.categoryId,
        selectedCity: selectedCity,
      );
    }

    setState(() => isLoadingLocation = false);
  }

  Future<void> _getCurrentLocation() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        // Use default Riyadh location
        currentLat = 24.7136;
        currentLng = 46.6753;
        return;
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          // Use default location
          currentLat = 24.7136;
          currentLng = 46.6753;
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        // Use default location
        currentLat = 24.7136;
        currentLng = 46.6753;
        return;
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      currentLat = position.latitude;
      currentLng = position.longitude;
    } catch (e) {
      // Use default Riyadh location if any error occurs
      currentLat = 24.7136;
      currentLng = 46.6753;
    }
  }

  Future<void> _loadCitiesWithLocation() async {
    try {
      final response = await widget.dioConsumer.post(
        '/api/general/cities',
        data: {
          'lat': currentLat ?? 24.7136,
          'lng': currentLng ?? 46.6753,
        },
      );

      if (response['data'] != null) {
        final data = response['data']['cities'] as List;
        cities = data.map((e) => City.fromJson(e)).toList();

        // Select the closest city (first in the list since API returns sorted by distance)
        if (cities.isNotEmpty) {
          selectedCity = cities.first;
        }
      }
    } catch (e) {
      cities = [];
      selectedCity = null;
    }
  }

  void _onCitySelected(City city) {
    setState(() => selectedCity = city);
    if (mounted) {
      _exploreListCubit.updateSelectedCity(city, widget.categoryId);
    }
  }

  void _onSearchChanged() {
    _applyFilters();
  }

  void _onScroll() {
    final exploreState = _exploreListCubit.state;
    if (exploreState is ExploreListLoaded &&
        _scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 100 &&
        !exploreState.isLoadingMore &&
        exploreState.hasMore) {
      _exploreListCubit.loadMorePlaces(
        categoryId: widget.categoryId,
        selectedCity: selectedCity,
      );
    }
  }

  Future<void> _onRefresh() async {
    await _exploreListCubit.refreshPlaces(
      categoryId: widget.categoryId,
      selectedCity: selectedCity,
    );
  }

  void _applyFilters() {
    _exploreListCubit.applyFilters(
      searchQuery: _searchController.text,
      priceRange: priceRange,
      minRating: selectedRating > 0 ? selectedRating : null,
      minGuests: selectedGuests > 1 ? selectedGuests : null,
      sortBy: selectedSortBy,
    );
  }

  void _showFilters() {
    ModernFilterBottomSheet.show(
      context,
      selectedSortBy: selectedSortBy,
      priceRange: priceRange,
      selectedRating: selectedRating,
      selectedGuests: selectedGuests,
      onSortChanged: (value) => setState(() => selectedSortBy = value),
      onPriceChanged: (value) => setState(() => priceRange = value),
      onRatingChanged: (value) => setState(() => selectedRating = value),
      onGuestsChanged: (value) => setState(() => selectedGuests = value),
      onReset: () {
        setState(() {
          selectedSortBy = 'price_low';
          priceRange = const RangeValues(0, 2000);
          selectedRating = 0.0;
          selectedGuests = 1;
        });
      },
      onApply: _applyFilters,
    );
  }

  void _navigateToDetails(place) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PlaceDetailsScreen(placeDetail: place),
      ),
    );
  }

  Future<void> _toggleFavorite(place) async {
    HapticFeedback.lightImpact();
    await GuestReservationHandler.handleFavorite(
      context: context,
      place: place,
      onToggleFavorite: () => _exploreListCubit.toggleFavorite(place),
      onLoginSuccess: _onRefresh,
    );
  }
}
