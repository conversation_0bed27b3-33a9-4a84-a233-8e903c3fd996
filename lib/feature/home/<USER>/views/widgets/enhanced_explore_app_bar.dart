import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/utils/guest_reservation_handler.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/City.dart';
import 'package:gather_point/feature/home/<USER>/views/widgets/explore_list_widgets.dart';
import 'package:gather_point/generated/l10n.dart';

class EnhancedExploreAppBar extends StatelessWidget {
  final String categoryTitle;
  final bool isCollapsed;
  final Animation<Offset> slideAnimation;
  final TextEditingController searchController;
  final City? selectedCity;
  final List<City> cities;
  final Function(City) onCitySelected;
  final VoidCallback? onSearchChanged;

  const EnhancedExploreAppBar({
    super.key,
    required this.categoryTitle,
    required this.isCollapsed,
    required this.slideAnimation,
    required this.searchController,
    this.selectedCity,
    required this.cities,
    required this.onCitySelected,
    this.onSearchChanged,
  });

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    final theme = Theme.of(context);

    return SliverAppBar(
      expandedHeight: 280,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: theme.colorScheme.primary,
      flexibleSpace: FlexibleSpaceBar(
        background: _buildBackground(context, theme),
      ),
      title: _buildCollapsedTitle(theme),
      centerTitle: true,
      leading: _buildBackButton(theme),
      actions: _buildActions(context, theme),
    );
  }

  Widget _buildBackground(BuildContext context, ThemeData theme) {
    final s = S.of(context);
    
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.primary,
            theme.colorScheme.primary.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: Stack(
        children: [
          _buildBackgroundPattern(theme),
          _buildContent(context, s, theme),
        ],
      ),
    );
  }

  Widget _buildBackgroundPattern(ThemeData theme) {
    return Positioned.fill(
      child: Opacity(
        opacity: 0.1,
        child: CustomPaint(
          painter: _ModernPatternPainter(
            color: theme.colorScheme.onPrimary,
          ),
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context, S s, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(24, 100, 24, 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTitle(theme),
          const SizedBox(height: 16),
          _buildCitySelection(),
          const SizedBox(height: 16),
          _buildSearchField(context, s, theme),
        ],
      ),
    );
  }

  Widget _buildTitle(ThemeData theme) {
    return AnimatedOpacity(
      opacity: isCollapsed ? 0.0 : 1.0,
      duration: const Duration(milliseconds: 300),
      child: SlideTransition(
        position: slideAnimation,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              categoryTitle,
              style: AppTextStyles.font32Bold.copyWith(
                color: Colors.white,
                height: 1.2,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'اكتشف أفضل الخيارات',
              style: AppTextStyles.font16Regular.copyWith(
                color: Colors.white.withValues(alpha: 0.9),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCitySelection() {
    return AnimatedOpacity(
      opacity: isCollapsed ? 0.0 : 1.0,
      duration: const Duration(milliseconds: 300),
      child: CitySelectionWidget(
        selectedCity: selectedCity,
        cities: cities,
        isLoading: false,
        onCitySelected: onCitySelected,
      ),
    );
  }

  Widget _buildSearchField(BuildContext context, S s, ThemeData theme) {
    return AnimatedOpacity(
      opacity: isCollapsed ? 0.0 : 1.0,
      duration: const Duration(milliseconds: 300),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: TextField(
          controller: searchController,
          onChanged: (_) => onSearchChanged?.call(),
          decoration: InputDecoration(
            prefixIcon: Icon(
              Icons.search_rounded,
              color: theme.colorScheme.primary,
              size: 24,
            ),
            suffixIcon: searchController.text.isNotEmpty
                ? IconButton(
                    icon: Icon(
                      Icons.clear_rounded,
                      color: Colors.grey[600],
                      size: 20,
                    ),
                    onPressed: () {
                      searchController.clear();
                      onSearchChanged?.call();
                      FocusScope.of(context).unfocus();
                    },
                  )
                : null,
            hintText: s.searchHint,
            hintStyle: AppTextStyles.font16Regular.copyWith(
              color: Colors.grey[500],
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: BorderSide.none,
            ),
            filled: true,
            fillColor: Colors.white,
            contentPadding: const EdgeInsets.symmetric(
              vertical: 16,
              horizontal: 20,
            ),
          ),
          style: AppTextStyles.font16Regular,
        ),
      ),
    );
  }

  Widget _buildCollapsedTitle(ThemeData theme) {
    return AnimatedOpacity(
      opacity: isCollapsed ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 200),
      child: Text(
        categoryTitle,
        style: AppTextStyles.font18Bold.copyWith(
          color: theme.colorScheme.onPrimary,
        ),
      ),
    );
  }

  Widget _buildBackButton(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: IconButton(
        icon: Icon(
          Icons.arrow_back_ios_rounded,
          color: Colors.white,
          size: 20,
        ),
        onPressed: () => Navigator.of(context).pop(),
      ),
    );
  }

  List<Widget> _buildActions(BuildContext context, ThemeData theme) {
    return [
      Padding(
        padding: const EdgeInsets.only(right: 8),
        child: GuestReservationHandler.getUserStatusBadge(context),
      ),
      Container(
        margin: const EdgeInsets.all(8),
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Center(
          child: Image.asset(
            'assets/images/logo_circle.png',
            width: 32,
            height: 32,
            fit: BoxFit.contain,
            errorBuilder: (context, error, stackTrace) => Icon(
              Icons.location_city_rounded,
              color: Colors.white,
              size: 24,
            ),
          ),
        ),
      ),
    ];
  }
}

class _ModernPatternPainter extends CustomPainter {
  final Color color;

  _ModernPatternPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    // Draw modern geometric pattern
    for (int i = 0; i < 5; i++) {
      final x = size.width * (i / 4);
      final y = size.height * 0.3;
      
      canvas.drawCircle(
        Offset(x, y),
        20 + (i * 5),
        paint,
      );
    }

    // Add some diagonal lines
    for (int i = 0; i < 3; i++) {
      final startX = size.width * (i / 2);
      final startY = size.height * 0.6;
      final endX = startX + 60;
      final endY = startY + 40;
      
      canvas.drawLine(
        Offset(startX, startY),
        Offset(endX, endY),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
