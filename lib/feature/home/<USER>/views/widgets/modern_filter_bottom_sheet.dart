import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/generated/l10n.dart';

class ModernFilterBottomSheet extends StatefulWidget {
  final String selectedSortBy;
  final RangeValues priceRange;
  final double selectedRating;
  final int selectedGuests;
  final Function(String) onSortChanged;
  final Function(RangeValues) onPriceChanged;
  final Function(double) onRatingChanged;
  final Function(int) onGuestsChanged;
  final VoidCallback onReset;
  final VoidCallback onApply;

  const ModernFilterBottomSheet({
    super.key,
    required this.selectedSortBy,
    required this.priceRange,
    required this.selectedRating,
    required this.selectedGuests,
    required this.onSortChanged,
    required this.onPriceChanged,
    required this.onRatingChanged,
    required this.onGuestsChanged,
    required this.onReset,
    required this.onApply,
  });

  @override
  State<ModernFilterBottomSheet> createState() => _ModernFilterBottomSheetState();

  static Future<void> show(
    BuildContext context, {
    required String selectedSortBy,
    required RangeValues priceRange,
    required double selectedRating,
    required int selectedGuests,
    required Function(String) onSortChanged,
    required Function(RangeValues) onPriceChanged,
    required Function(double) onRatingChanged,
    required Function(int) onGuestsChanged,
    required VoidCallback onReset,
    required VoidCallback onApply,
  }) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ModernFilterBottomSheet(
        selectedSortBy: selectedSortBy,
        priceRange: priceRange,
        selectedRating: selectedRating,
        selectedGuests: selectedGuests,
        onSortChanged: onSortChanged,
        onPriceChanged: onPriceChanged,
        onRatingChanged: onRatingChanged,
        onGuestsChanged: onGuestsChanged,
        onReset: onReset,
        onApply: onApply,
      ),
    );
  }
}

class _ModernFilterBottomSheetState extends State<ModernFilterBottomSheet>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  late String localSortBy;
  late RangeValues localPriceRange;
  late double localRating;
  late int localGuests;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _slideAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    // Initialize local values
    localSortBy = widget.selectedSortBy;
    localPriceRange = widget.priceRange;
    localRating = widget.selectedRating;
    localGuests = widget.selectedGuests;

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    final theme = Theme.of(context);
    final mediaQuery = MediaQuery.of(context);

    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0, 1),
          end: Offset.zero,
        ).animate(_slideAnimation),
        child: Container(
          height: mediaQuery.size.height * 0.8,
          decoration: BoxDecoration(
            color: theme.cardColor,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 20,
                offset: const Offset(0, -4),
              ),
            ],
          ),
          child: Column(
            children: [
              _buildHeader(context, s, theme),
              Expanded(
                child: _buildContent(context, s, theme),
              ),
              _buildFooter(context, s, theme),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, S s, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Text(
                'فلترة النتائج',
                style: AppTextStyles.font20Bold.copyWith(
                  color: theme.textTheme.bodyLarge?.color,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: () {
                  HapticFeedback.lightImpact();
                  setState(() {
                    localSortBy = 'price_low';
                    localPriceRange = const RangeValues(0, 2000);
                    localRating = 0.0;
                    localGuests = 1;
                  });
                },
                child: Text(
                  'إعادة تعيين',
                  style: AppTextStyles.font14SemiBold.copyWith(
                    color: theme.colorScheme.primary,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context, S s, ThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSortSection(s, theme),
          const SizedBox(height: 32),
          _buildPriceSection(s, theme),
          const SizedBox(height: 32),
          _buildRatingSection(s, theme),
          const SizedBox(height: 32),
          _buildGuestsSection(s, theme),
        ],
      ),
    );
  }

  Widget _buildFooter(BuildContext context, S s, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Colors.grey.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () {
                HapticFeedback.lightImpact();
                Navigator.pop(context);
              },
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                'إلغاء',
                style: AppTextStyles.font16SemiBold,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: () {
                HapticFeedback.lightImpact();
                widget.onSortChanged(localSortBy);
                widget.onPriceChanged(localPriceRange);
                widget.onRatingChanged(localRating);
                widget.onGuestsChanged(localGuests);
                widget.onApply();
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                'تطبيق الفلاتر',
                style: AppTextStyles.font16SemiBold.copyWith(
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSortSection(S s, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'ترتيب حسب',
          style: AppTextStyles.font16Bold.copyWith(
            color: theme.textTheme.bodyLarge?.color,
          ),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: [
            _buildSortChip('price_low', 'السعر: من الأقل للأعلى', theme),
            _buildSortChip('price_high', 'السعر: من الأعلى للأقل', theme),
            _buildSortChip('rating_high', 'التقييم: الأعلى أولاً', theme),
            _buildSortChip('popular', 'الأكثر شعبية', theme),
            _buildSortChip('newest', 'الأحدث', theme),
          ],
        ),
      ],
    );
  }

  Widget _buildSortChip(String value, String label, ThemeData theme) {
    final isSelected = localSortBy == value;

    return FilterChip(
      selected: isSelected,
      label: Text(
        label,
        style: AppTextStyles.font14Regular.copyWith(
          color: isSelected ? Colors.white : theme.textTheme.bodyLarge?.color,
        ),
      ),
      onSelected: (selected) {
        if (selected) {
          setState(() => localSortBy = value);
          HapticFeedback.lightImpact();
        }
      },
      backgroundColor: theme.cardColor,
      selectedColor: theme.colorScheme.primary,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
        side: BorderSide(
          color: isSelected ? theme.colorScheme.primary : Colors.grey[300]!,
        ),
      ),
    );
  }

  Widget _buildPriceSection(S s, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نطاق السعر',
          style: AppTextStyles.font16Bold.copyWith(
            color: theme.textTheme.bodyLarge?.color,
          ),
        ),
        const SizedBox(height: 16),
        RangeSlider(
          values: localPriceRange,
          min: 0,
          max: 2000,
          divisions: 20,
          labels: RangeLabels(
            '${localPriceRange.start.round()} ريال',
            '${localPriceRange.end.round()} ريال',
          ),
          onChanged: (values) {
            setState(() => localPriceRange = values);
            HapticFeedback.selectionClick();
          },
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '${localPriceRange.start.round()} ريال',
              style: AppTextStyles.font14Regular.copyWith(
                color: Colors.grey[600],
              ),
            ),
            Text(
              '${localPriceRange.end.round()} ريال',
              style: AppTextStyles.font14Regular.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRatingSection(S s, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'التقييم الأدنى',
          style: AppTextStyles.font16Bold.copyWith(
            color: theme.textTheme.bodyLarge?.color,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: List.generate(5, (index) {
            final rating = index + 1.0;
            final isSelected = localRating >= rating;

            return GestureDetector(
              onTap: () {
                setState(() {
                  localRating = localRating == rating ? 0.0 : rating;
                });
                HapticFeedback.lightImpact();
              },
              child: Container(
                margin: const EdgeInsets.only(right: 8),
                child: Icon(
                  isSelected ? Icons.star : Icons.star_border,
                  color: Colors.amber,
                  size: 32,
                ),
              ),
            );
          }),
        ),
        const SizedBox(height: 8),
        Text(
          localRating > 0 ? '${localRating.toInt()} نجوم وأكثر' : 'جميع التقييمات',
          style: AppTextStyles.font14Regular.copyWith(
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildGuestsSection(S s, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'عدد الضيوف',
          style: AppTextStyles.font16Bold.copyWith(
            color: theme.textTheme.bodyLarge?.color,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            IconButton(
              onPressed: localGuests > 1
                  ? () {
                      setState(() => localGuests--);
                      HapticFeedback.lightImpact();
                    }
                  : null,
              icon: const Icon(Icons.remove_circle_outline),
              color: theme.colorScheme.primary,
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '$localGuests',
                style: AppTextStyles.font16Bold.copyWith(
                  color: theme.textTheme.bodyLarge?.color,
                ),
              ),
            ),
            IconButton(
              onPressed: localGuests < 20
                  ? () {
                      setState(() => localGuests++);
                      HapticFeedback.lightImpact();
                    }
                  : null,
              icon: const Icon(Icons.add_circle_outline),
              color: theme.colorScheme.primary,
            ),
            const SizedBox(width: 16),
            Text(
              localGuests == 1 ? 'ضيف واحد' : '$localGuests ضيوف',
              style: AppTextStyles.font14Regular.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ],
    );
  }
}
