import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/generated/l10n.dart';

class ExploreFilterBar extends StatelessWidget {
  final VoidCallback onFilterTap;

  const ExploreFilterBar({
    super.key,
    required this.onFilterTap,
  });

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    final theme = Theme.of(context);

    return SliverToBoxAdapter(
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: Text(
                s.searchResults,
                style: AppTextStyles.font16Bold.copyWith(
                  color: theme.textTheme.bodyLarge?.color,
                ),
              ),
            ),
            IconButton(
              onPressed: onFilterTap,
              icon: Icon(
                Icons.tune,
                color: theme.colorScheme.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
