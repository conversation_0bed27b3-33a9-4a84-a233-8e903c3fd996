import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/generated/l10n.dart';

class EnhancedSearchComponent extends StatefulWidget {
  final TextEditingController controller;
  final Function(String) onSearchChanged;
  final Function(String) onSuggestionSelected;
  final List<String> suggestions;
  final bool showSuggestions;

  const EnhancedSearchComponent({
    super.key,
    required this.controller,
    required this.onSearchChanged,
    required this.onSuggestionSelected,
    this.suggestions = const [],
    this.showSuggestions = true,
  });

  @override
  State<EnhancedSearchComponent> createState() => _EnhancedSearchComponentState();
}

class _EnhancedSearchComponentState extends State<EnhancedSearchComponent>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  
  final FocusNode _focusNode = FocusNode();
  bool _showSuggestions = false;
  
  // Popular search suggestions
  final List<String> _popularSuggestions = [
    'فنادق فاخرة',
    'مطاعم عائلية',
    'كافيهات',
    'أماكن ترفيهية',
    'منتجعات',
    'شاليهات',
    'استراحات',
    'قاعات أفراح',
  ];

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 0.95,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _focusNode.addListener(_onFocusChanged);
    widget.controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _animationController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onFocusChanged() {
    if (_focusNode.hasFocus && widget.showSuggestions) {
      setState(() => _showSuggestions = true);
      _animationController.forward();
    } else {
      _animationController.reverse().then((_) {
        if (mounted) setState(() => _showSuggestions = false);
      });
    }
  }

  void _onTextChanged() {
    widget.onSearchChanged(widget.controller.text);
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    final theme = Theme.of(context);

    return Column(
      children: [
        _buildSearchField(context, s, theme),
        if (_showSuggestions) _buildSuggestions(context, theme),
      ],
    );
  }

  Widget _buildSearchField(BuildContext context, S s, ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: TextField(
        controller: widget.controller,
        focusNode: _focusNode,
        decoration: InputDecoration(
          prefixIcon: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            child: Icon(
              _focusNode.hasFocus ? Icons.search_rounded : Icons.search,
              color: _focusNode.hasFocus 
                  ? theme.colorScheme.primary 
                  : Colors.grey[600],
              size: 24,
            ),
          ),
          suffixIcon: widget.controller.text.isNotEmpty
              ? Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (widget.controller.text.length > 2)
                      IconButton(
                        icon: Icon(
                          Icons.mic_rounded,
                          color: theme.colorScheme.primary,
                          size: 20,
                        ),
                        onPressed: _startVoiceSearch,
                      ),
                    IconButton(
                      icon: Icon(
                        Icons.clear_rounded,
                        color: Colors.grey[600],
                        size: 20,
                      ),
                      onPressed: () {
                        widget.controller.clear();
                        FocusScope.of(context).unfocus();
                        HapticFeedback.lightImpact();
                      },
                    ),
                  ],
                )
              : IconButton(
                  icon: Icon(
                    Icons.tune_rounded,
                    color: Colors.grey[600],
                    size: 20,
                  ),
                  onPressed: _showAdvancedFilters,
                ),
          hintText: s.searchHint,
          hintStyle: AppTextStyles.font16Regular.copyWith(
            color: Colors.grey[500],
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide.none,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(
              color: theme.colorScheme.primary,
              width: 2,
            ),
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding: const EdgeInsets.symmetric(
            vertical: 16,
            horizontal: 20,
          ),
        ),
        style: AppTextStyles.font16Regular,
        textInputAction: TextInputAction.search,
        onSubmitted: (value) {
          if (value.isNotEmpty) {
            widget.onSuggestionSelected(value);
            _focusNode.unfocus();
          }
        },
      ),
    );
  }

  Widget _buildSuggestions(BuildContext context, ThemeData theme) {
    final suggestions = _getSuggestions();
    
    if (suggestions.isEmpty) return const SizedBox.shrink();

    return FadeTransition(
      opacity: _fadeAnimation,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Container(
          margin: const EdgeInsets.only(top: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (widget.controller.text.isEmpty) ...[
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    'البحث الشائع',
                    style: AppTextStyles.font14Bold.copyWith(
                      color: Colors.grey[700],
                    ),
                  ),
                ),
              ],
              ...suggestions.take(5).map((suggestion) => _buildSuggestionItem(
                context,
                suggestion,
                theme,
              )),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSuggestionItem(BuildContext context, String suggestion, ThemeData theme) {
    final isRecent = widget.controller.text.isEmpty;
    
    return InkWell(
      onTap: () {
        widget.controller.text = suggestion;
        widget.onSuggestionSelected(suggestion);
        _focusNode.unfocus();
        HapticFeedback.lightImpact();
      },
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Icon(
              isRecent ? Icons.history_rounded : Icons.search_rounded,
              size: 20,
              color: Colors.grey[600],
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                suggestion,
                style: AppTextStyles.font14Regular.copyWith(
                  color: theme.textTheme.bodyLarge?.color,
                ),
              ),
            ),
            Icon(
              Icons.north_west_rounded,
              size: 16,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }

  List<String> _getSuggestions() {
    final query = widget.controller.text.toLowerCase();
    
    if (query.isEmpty) {
      return _popularSuggestions;
    }
    
    // Filter suggestions based on query
    final filtered = [
      ...widget.suggestions,
      ..._popularSuggestions,
    ].where((suggestion) => 
      suggestion.toLowerCase().contains(query)
    ).toList();
    
    // Remove duplicates
    return filtered.toSet().toList();
  }

  void _startVoiceSearch() {
    HapticFeedback.lightImpact();
    // TODO: Implement voice search functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('البحث الصوتي قريباً'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _showAdvancedFilters() {
    HapticFeedback.lightImpact();
    // TODO: Show advanced filters bottom sheet
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('الفلاتر المتقدمة قريباً'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}
