import 'package:flutter/material.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/feature/home/<USER>/views/explore_list_view.dart';

/// Example of how to use the enhanced ExploreListScreen
/// 
/// This example shows how to navigate to the explore list view
/// with shimmer loading, city selection, and clean architecture
class ExampleUsage extends StatelessWidget {
  final DioConsumer dioConsumer;

  const ExampleUsage({super.key, required this.dioConsumer});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Categories')),
      body: ListView(
        children: [
          // Example category tiles
          _buildCategoryTile(
            context: context,
            title: 'Hotels',
            categoryId: 1,
            icon: Icons.hotel,
          ),
          _buildCategoryTile(
            context: context,
            title: 'Restaurants',
            categoryId: 2,
            icon: Icons.restaurant,
          ),
          _buildCategoryTile(
            context: context,
            title: 'Entertainment',
            categoryId: 3,
            icon: Icons.local_activity,
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryTile({
    required BuildContext context,
    required String title,
    required int categoryId,
    required IconData icon,
  }) {
    return ListTile(
      leading: Icon(icon),
      title: Text(title),
      trailing: const Icon(Icons.arrow_forward_ios),
      onTap: () {
        // Navigate to enhanced explore list view
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ExploreListScreen(
              categoryId: categoryId,
              categoryTitle: title,
              dioConsumer: dioConsumer,
            ),
          ),
        );
      },
    );
  }
}

/// Features of the Enhanced ExploreListScreen:
/// 
/// ✅ Shimmer Loading Effects:
/// - Beautiful shimmer placeholders while loading
/// - Matches exact layout of content cards
/// - Smooth animations
/// 
/// ✅ City Selection:
/// - Dropdown in app bar to select city
/// - Shows distance from user location
/// - Filters results by selected city
/// - Loading state with shimmer
/// 
/// ✅ Clean Architecture:
/// - ExploreListCubit for state management
/// - Separated widgets for reusability
/// - PlaceCard widget for consistent design
/// - Proper error handling
/// 
/// ✅ Enhanced UI:
/// - Improved app bar with city selection
/// - Better search with clear button
/// - Loading states with shimmer
/// - Error states with retry functionality
/// 
/// ✅ State Management:
/// - BlocProvider automatically provided
/// - Proper loading, loaded, and error states
/// - Pagination support
/// - Filter and sort functionality
/// 
/// Usage is exactly the same as before:
/// ```dart
/// ExploreListScreen(
///   categoryId: categoryId,
///   categoryTitle: categoryTitle,
///   dioConsumer: dioConsumer,
/// )
/// ```
/// 
/// The enhanced features work automatically without any additional setup!
