import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/managers/app_initialization_cubit/app_initialization_cubit.dart';
import 'package:gather_point/core/routing/routes_keys.dart';
import 'package:gather_point/core/utils/app_assets.dart';
import 'package:go_router/go_router.dart';

class SplashViewBody extends StatefulWidget {
  const SplashViewBody({super.key});

  @override
  State<SplashViewBody> createState() => _SplashViewBodyState();
}

class _SplashViewBodyState extends State<SplashViewBody>
    with SingleTickerProviderStateMixin {
  late AnimationController animationController;
  late Animation<Offset> slideAnimation;

  @override
  void initState() {
    initSlidingAnimation();
    initializeApp();
    super.initState();
  }

  @override
  void dispose() {
    animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AppInitializationCubit, AppInitializationState>(
      listener: (context, state) {
        if (state is AppInitializationShowOnboarding) {
          if (mounted) {
            GoRouter.of(context).go(RoutesKeys.kOnBoardingView);
          }
        } else if (state is AppInitializationNavigateToHome) {
          if (mounted) {
            GoRouter.of(context).go(RoutesKeys.kHomeViewTab);
          }
        } else if (state is AppInitializationNavigateToLogin) {
          if (mounted) {
            GoRouter.of(context).go(RoutesKeys.kLoginView);
          }
        } else if (state is AppInitializationError) {
          // On error, still navigate to onboarding as fallback
          if (mounted) {
            GoRouter.of(context).go(RoutesKeys.kOnBoardingView);
          }
        }
      },
      child: AnimatedBuilder(
        animation: animationController,
        builder: (context, _) {
          return SlideTransition(
            position: slideAnimation,
            child: Center(
              child: Image.asset(AppAssets.imagesSplash, width: 300, height: 300),
            ),
          );
        },
      ),
    );
  }

  void initSlidingAnimation() {
    animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    final CurvedAnimation curvedAnimation = CurvedAnimation(
      parent: animationController,
      curve: Curves.easeInOut,
    );

    // Define the sliding animation
    slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 1.0), // Start from the bottom (off-screen)
      end: const Offset(0.0, 0.0), // Move to the center (off-screen)
    ).animate(curvedAnimation);

    animationController.forward();
  }

  void initializeApp() {
    Future.delayed(
      const Duration(milliseconds: 2500),
      () {
        if (mounted) {
          context.read<AppInitializationCubit>().initializeApp();
        }
      },
    );
  }
}
